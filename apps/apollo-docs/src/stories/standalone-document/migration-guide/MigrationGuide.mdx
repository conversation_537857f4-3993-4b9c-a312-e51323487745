import { Meta } from "@storybook/addon-docs/blocks"

import { DiffCodeBlock } from "./DiffCodeBlock"
import StorybookLink from "../../../components/storybook-link/StorybookLink"

<Meta title="Migration Guide" tags={["docs"]} />

# Migration Guide

<div style={{ lineHeight: "1.8" }} >

- [Intro](#intro)
- [What’s new ✨](#What’s-new-)
- [Migration Steps 🚀](#migration-steps-)
- [Breaking Changes 🚨](#breaking-changes-)
  - [Alert Component](#alert-component)
  - [Autocomplete Component](#autocomplete-component)
  - [Button Component](#button-component)
  - [Checkbox Component](#checkbox-component)
  - [Chip Component](#chip-component)
  - [FloatButton Component](#floatbutton-component)
  - [IconButton Component](#iconbutton-component)
  - [Input Component](#input-component)
  - [DatePicker Component](#datepicker-component)
  - [Modal Component](#modal-component)
  - [Radio Component](#radio-component)
  - [Select Component](#select-component)
  - [Switch Component](#switch-component)
  - [Textarea Component](#textarea-component)
  - [Toast Component](#toast-component)
  - [Typography Component](#typography-component)
  - [UploadBox Component](#uploadbox-component)
- [Help 🙏](#help-)

## Intro

This guide helps you migrate from the legacy design system (`@design-systems/apollo-ui` / `@apollo/ui/legacy` packages) to the new Apollo package `@apollo/ui`.

## What’s new ✨

- Unified package: import components from `@apollo/ui`
- Token‑based styling: consistent visuals via Apollo alias tokens (colors, spacing, radii, states)
- Clear breaking changes: some legacy props and class hooks were removed/renamed (documented per component)
- <StorybookLink page="apollo∕ui-components-inputs-textarea">Textarea</StorybookLink> component
- <StorybookLink page="apollo∕ui-components-data-display-badge">Badge</StorybookLink> component
- <StorybookLink page="apollo∕ui-components-utilities-portal">Portal</StorybookLink> component

## Migration Steps 🚀

1. Install `@apollo/ui` and remove `@design-systems/apollo-ui`:

```bash
# with pnpm
pnpm add @apollo/ui
pnpm remove @design-systems/apollo-ui
```

```bash
# with yarn
yarn add @apollo/ui
yarn remove @design-systems/apollo-ui
```

```bash
# with npm
npm install @apollo/ui
npm uninstall @design-systems/apollo-ui
```

2. Follow the changes in the Breaking Changes 🚨 section below and apply any manual changes required.
3. Review the changes and test your application thoroughly to ensure everything works as expected.

---

## Breaking Changes 🚨

Several breaking changes have been made to optimize and streamline the library. These changes include the removal of deprecated components, updates to component APIs, and overall enhancements.

#### Imports


<DiffCodeBlock code={`- import { Input } from "@apollo/ui/legacy"
+ import { Input } from "@apollo/ui"
`}
  />


Migration: Replace `@apollo/ui/legacy` or `@design-systems/apollo-ui` with `@apollo/ui`.

## Alert Component

- **Prop `color` -> `type` instead
- **Type name changed**: `colorProp` → `TypeProp`
- **Type values changed**: `"info"` → `"information"`
- **New action prop added**: `action` prop for inline action buttons

<DiffCodeBlock
  code={`- <Alert
-   color="warning"
-   title="Storage Full"
-   description="Please free up space"
- />

+ <Alert
+   color="warning"
+   title="Storage Full"
+   description="Please free up space"
+   action={
+     <Button size="small" variant="outline">
+       Upgrade Plan
+     </Button>
+   }
+ />`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-feedback-alert">Alert</StorybookLink> docs.

---

## Autocomplete Component

- `onChange` signature changed `MUI-style` → `value-first`
- `Search control` renamed `inputValue/onInputChange` → `search/onSearch with debounce`
- `Select All` API renamed `hasSelectAll/allOption/onClickAllOption` → `showSelectAll/selectAllText`
- `Load More` API renamed `hasLoadMore` → `hasMore`; add `loadingMore`
- `Empty state` prop renamed `noItemLabel` → `noOptionsComponent`
- `Customization` via slots/slotProps and popupProps/disablePortal removed; use `className/inputProps/menuProps`
- `AutocompleteOption` no longer supports per-item className/onClick; handle styling/actions differently
- `createFilterOptions` export removed; use `filterLogic`
- `Misc legacy props` not supported: limitTags, hideOverflowTag, isClearSearchKeywordAfterSelect, disableClearable

### onChange prop

Legacy MUI-style `(event, value)` changed to `(value, event?)`.

<DiffCodeBlock
  code={`- <Autocomplete options={opts} value={val} onChange={(e, v) => setVal(v)} />
+ <Autocomplete options={opts} value={val} onChange={(v) => setVal(v)} />
`}
/>

Multiple mode keeps value as `any[]`:

<DiffCodeBlock
  code={`- <Autocomplete multiple value={vals} onChange={(e, v) => setVals(v)} />
+ <Autocomplete multiple value={vals} onChange={(v) => setVals(v)} />
`}
/>

### Search control

- Legacy: `inputValue` + `onInputChange(event, value, reason, focused?)`
- Apollo: `search` + `onSearch(value)` with built-in debounce (configurable via `debounceMs`)

<DiffCodeBlock
  code={`- <Autocomplete inputValue={q} onInputChange={(_, v) => setQ(v)} />
+ <Autocomplete search={q} onSearch={setQ} debounceMs={300} />
`}
/>

To disable the inline search box in the popup, use `disableSearch`.

### Select All API

- Legacy: `hasSelectAll`, `allOption`, optional `onClickAllOption`
- Apollo: `showSelectAll`, `selectAllText` (applies in multiple mode; toggles visible filtered options)

<DiffCodeBlock
  code={`- <Autocomplete multiple hasSelectAll allOption={{ label: "All" }} />
+ <Autocomplete multiple showSelectAll selectAllText="Select All" />
`}
/>

If you previously used `onClickAllOption` for custom behavior, move that logic to the component’s `onChange` handler.

### Load more / infinite scroll

- Legacy: `hasLoadMore`, `onLoadMore`, `loadMoreLabel`
- Apollo: `hasMore`, `loadingMore`, `onLoadMore`, `loadMoreLabel`

<DiffCodeBlock
  code={`- <Autocomplete hasLoadMore onLoadMore={loadMore} loadMoreLabel="Loading…" />
+ <Autocomplete hasMore loadingMore onLoadMore={loadMore} loadMoreLabel={<>Loading…</>} />
`}
/>

### Empty state

- Legacy: `noItemLabel` (string)
- Apollo: `noOptionsComponent` (ReactNode)

<DiffCodeBlock
  code={`- <Autocomplete noItemLabel="No item" />
+ <Autocomplete noOptionsComponent={<div>No items</div>} />
`}
/>

### Customization and theming

- Removed: `slots`, `slotProps`, `popupProps`, `disablePortal`
- Use: `className`/`style` for root; `inputProps` for input element; `menuProps` for the options list; decorators: `labelDecorator`, `helperTextDecorator`

Example:

```tsx
<Autocomplete
  className="my-autocomplete"
  inputProps={{ className: "my-input" }}
  menuProps={{ className: "my-menu" }}
/>
```

### Option shape and per-item actions

- Legacy `AutocompleteOption` supported `className` and `onClick`
- Apollo option: `{ label, value, disabled? }`
- Styling per-item: use `menuProps`/global classes; custom behaviors: branch in `onChange`

```tsx
// Before
const options = [{ label: "A", value: "a", onClick: fn }]
// After
const options = [{ label: "A", value: "a" }]
```

### Filtering utilities

- Removed named export: `createFilterOptions`
- Use: `filterLogic` prop or pre-filter your `options`

```tsx
<Autocomplete
  options={options}
  filterLogic={(opts, q) => opts.filter((o) => o.label.includes(q))}
/>
```

### Other removed/changed props

- `limitTags`, `hideOverflowTag`, `isClearSearchKeywordAfterSelect`, `disableClearable` are not supported. If you relied on these, consider truncating via CSS or handling clear behavior externally.
- `searchable={false}` → use `disableSearch`.

### Accessibility

The new component exposes top-level accessibility props: `id`, `name`, `autoFocus`, `tabIndex`, `aria-label`, `aria-labelledby`, `aria-describedby`. Menu/list ARIA is handled internally; pass-through attributes to the list via `menuProps`.

No additional setup is required beyond installing `@apollo/ui`; external MUI Base dependencies are no longer needed.

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-autocomplete">Autocomplete</StorybookLink> docs.

---

## Button Component

- **Variant names changed**: `solid` → `filled`, `plain` → `text`
- **Size names changed**: `sm`/`md`/`lg` → `small`/`large` (removed medium)
- **Color names changed**: `danger` → `negative`
- **Loading functionality removed**: `loading`, `loadingPosition`, `loadingIndicator` props no longer supported
- **Polymorphic rendering changed**: `as` prop removed, use `href` for links
- **Action prop removed**: `action` prop for imperative focus no longer supported

### Variant

The variant names have changed to be more descriptive:

<DiffCodeBlock
  code={`- <Button variant="solid">Primary Action</Button>
+ <Button variant="filled">Primary Action</Button>

- <Button variant="plain">Text Button</Button>
+ <Button variant="text">Text Button</Button>

// outline variant remains the same
<Button variant="outline">Secondary Action</Button>`}
/>

### Size

Size options have been simplified from three to two sizes:

<DiffCodeBlock
  code={`- <Button size="sm">Small</Button>
+ <Button size="small">Small</Button>

- <Button size="md">Medium</Button>
+ <Button size="large">Large (default)</Button>

- <Button size="lg">Large</Button>
+ <Button size="large">Large</Button>`}
/>

### Color

The `danger` color has been renamed to `negative`:

<DiffCodeBlock
  code={`- <Button color="danger">Delete</Button>
+ <Button color="negative">Delete</Button>

// primary color remains the same
<Button color="primary">Save</Button>`}
/>

### Loading State

**⚠️ Breaking Change**: Loading functionality has been completely removed from the new Button component. You'll need to implement loading states manually:

<DiffCodeBlock
  code={`- <Button loading loadingPosition="start">
-   Saving...
- </Button>
+ <Button
+   disabled={loading}
+   startDecorator={loading && <LoadingIcon />}
+ >
+   {loading ? "Saving..." : "Save"}
+ </Button>`}
/>

For loading indicators, you can use icons from `@design-systems/apollo-icons`:

```tsx
import { Loading3Quarters } from "@design-systems/apollo-icons"

<Button
  disabled={loading}
  startDecorator={
    loading && (
      <Loading3Quarters
        size={16}
        style={{ animation: "spin 1s linear infinite" }}
      />
    )
  }
>
  {loading ? "Loading..." : "Click me"}
</Button>
```

### Link Button

The `as` prop has been removed. Use the `href` prop to render buttons as links:

<DiffCodeBlock
  code={`- <Button as="a" href="/dashboard">
-   Go to Dashboard
- </Button>
+ <Button href="/dashboard">
+   Go to Dashboard
+ </Button>`}
/>

### Removed Props

These props are no longer supported in the new Button component:

- `loading` - Implement loading states manually
- `loadingPosition` - Use `startDecorator` or `endDecorator`
- `loadingIndicator` - Use custom loading icons
- `as` - Use `href` for links, or wrap with your own element
- `action` - Imperative focus control no longer supported
- `tabIndex` - Use standard HTML `tabIndex` attribute

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-button">Button</StorybookLink> docs.

---

## Checkbox Component

- **Label prop type changed**: `string` → `ReactNode` (supports rich content)
- **Label placement removed**: `labelPlacement` prop no longer supported (always right-aligned)
- **Slot props removed**: `slotProps` customization no longer supported
- **onChange signature changed**: Standard HTML event → `(event: Event, checked: boolean) => void`
- **New ref props**: `rootRef`, `wrapperRef` for advanced ref management
- **CSS class hooks changed**: Legacy class names updated to new Apollo naming

### Label Placement

Label placement options have been removed. Labels are now always positioned to the right of the checkbox:

<DiffCodeBlock
  code={`- <Checkbox label="Option" labelPlacement="top" />
- <Checkbox label="Option" labelPlacement="left" />
- <Checkbox label="Option" labelPlacement="bottom" />
+ <Checkbox label="Option" />
+ // All labels now appear to the right of the checkbox`}
/>

If you need custom label positioning, wrap the checkbox in your own layout container.

### Event Handling

The onChange signature has changed to provide both event and checked state:

<DiffCodeBlock
  code={`- <Checkbox
-   onChange={(e) => setChecked(e.target.checked)}
- />
+ <Checkbox
+   onChange={(event, checked) => setChecked(checked)}
+ />`}
/>

### Customization and Styling

Slot props have been removed in favor of standard className and new ref props:

<DiffCodeBlock
  code={`- <Checkbox
-   slotProps={{
-     root: { className: "custom-root" }
-   }}
- />
+ <Checkbox
+   className="custom-checkbox"
+   rootRef={rootRef}
+   wrapperRef={wrapperRef}
+ />`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-checkbox">Checkbox</StorybookLink> documentation.

---

## Chip Component

- **Variant names changed**: `"fill"` → `"filled"` (outline remains the same)
- **Color options reduced**: `"default" | "primary" | "success" | "warning" | "danger" | "process"` → `"primary" | "negative"`
- **Default variant changed**: `"outline"` → `"filled"`
- **Default color changed**: `"default"` → `"primary"`
- **Rounded prop removed**: `rounded` prop with multiple border radius options no longer supported
- **Label prop type changed**: `React.ReactNode` → `string` (no longer supports JSX content)
- **Text truncation removed**: `truncatedTextWidth` prop no longer supported
- **Start decorator removed**: `startDecorator` prop for icons/content before label no longer supported
- **Delete functionality changed**: `onDelete` + `deleteIcon` → `onClose` (simplified API)
- **New check functionality**: Added `onCheck` prop for selectable/toggleable chips
- **Size options added**: New `size` prop with `"large" | "medium" | "small"` options
- **ID props removed**: `idIconClose` and `idLabel` props no longer supported

### Variant

The variant names have been updated and the default variant has changed from `outline` to `filled`:

<DiffCodeBlock
  code={`- <Chip variant="fill" label="Filled Chip" />
+ <Chip variant="filled" label="Filled Chip" />`}
/>

### Color

Color options have been significantly reduced and renamed:

<DiffCodeBlock
  code={`- <Chip color="danger" label="Error" />
+ <Chip color="negative" label="Error" />

- <Chip color="default" label="Default" />
+ <Chip color="primary" label="Default" />`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-data-display-chip">Chip</StorybookLink> docs.

---

## FloatButton Component

- **Props unchanged**: `icon`, `label`, `isExpanded`, `iconSide`, `disabled`
- **New property added**: `href` for link functionality

<DiffCodeBlock
  code={`// New functionality - render as link
+ <FloatButton
+   href="/compose"
+   icon={<Edit />}
+   label="Compose"
+   isExpanded
+ >`}
/>

This allows FloatButton to function as navigation elements while maintaining the same visual appearance.

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-floatbutton">FloatButton</StorybookLink> docs.

---

## IconButton Component

- **Import path changed**: `@design-systems/apollo-ui` → `@apollo/ui`
- **Size options reduced**: `"small" | "medium" | "large"` → `"small" | "large"` (medium removed)
- **Default size changed**: Legacy defaults to `"medium"`, New defaults to `"large"`
- **Variant system introduced**: New `variant` prop with `"filled" | "outline" | "icon"` options
- **Default variant**: New component defaults to `"icon"` variant (renders as text button)
- **Color options updated**: `"danger"` → `"negative"` (consistent with Button component)
- **New link behavior**: Use `href` prop to render as anchor element

### Size

The medium size option has been removed, and the default size has changed:

<DiffCodeBlock
  code={`- <IconButton size="small"><Heart /></IconButton>
+ <IconButton size="small"><Heart /></IconButton>

- <IconButton size="medium"><Heart /></IconButton>
+ <IconButton size="small"><Heart /></IconButton>  // or size="large"

- <IconButton size="large"><Heart /></IconButton>
+ <IconButton size="large"><Heart /></IconButton>

- <IconButton><Heart /></IconButton>  // Legacy default: medium
+ <IconButton><Heart /></IconButton>  // New default: large`}
/>

**Migration Strategy for Medium Size:**
- For compact interfaces (toolbars, dense layouts): Use `size="small"`
- For standard interfaces: Use `size="large"` (new default)
- Review your design to determine which size better fits your use case

### Variant

The new IconButton introduces a `variant` prop that controls the visual style:

<DiffCodeBlock
  code={`// Legacy (no variant prop - always rendered as text button)
- <IconButton><Heart /></IconButton>

// New (explicit variant control)
+ <IconButton variant="icon"><Heart /></IconButton>     // Default: text button style
+ <IconButton variant="filled"><Heart /></IconButton>   // Filled background
+ <IconButton variant="outline"><Heart /></IconButton>  // Outlined border`}
/>

**Default Behavior:**
- Legacy: Always rendered as a text-style button
- Apollo: Defaults to `variant="icon"` which renders as a text-style button (same visual result)

### Color

Color prop values have been updated to match the Button component:

<DiffCodeBlock
  code={`- <IconButton color="danger"><Trash /></IconButton>
+ <IconButton color="negative"><Trash /></IconButton>`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-iconbutton">IconButton</StorybookLink> docs.

---

## Input Component

This section consolidates all breaking changes for the Input component when migrating to the new Apollo Input in `packages/apollo-ui` (imported from `@apollo/ui`).

To replace old input usages:

- `variant` (e.g. `outline`) → one visual style; use `error` to indicate invalid state
- `color` (e.g. `primary`, `danger`) → unified styling; use `error` for error visuals
- `multiline` (+ `rows`, `minRows`, `maxRows`) → use Textarea component

<ul>
  <DiffCodeBlock
    code={`- <Input variant="outline" color="danger"  />
+ <Input error>
`}
  />
</ul>

<ul>
  <DiffCodeBlock
    code={`- <Input multiline />
+ <Textarea>
`}
  />
</ul>

New props:

- `labelDecorator`, `helperTextDecorator` – augment label/helper text
- `hasCharacterCount` – built‑in character counter support (uses `maxLength` to show denominator)
- `rootProps`, `rootRef` – pass props/ref to the control root div
- `fieldProps` – pass-through props to the surrounding Field (layout, className, etc.)


#### Migration tips

- Remove styling overrides targeting legacy Tailwind/CVA utility classes and legacy hooks like `.ApolloInput-root` or `.ApolloInput-input`
- Prefer composing with `className`, `rootProps`, and `fieldProps` and rely on tokens for theme‑consistent visuals

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-input">Input</StorybookLink> docs.

---

## DatePicker Component

The DatePicker component has undergone significant changes when migrating from the legacy DateInput component in `packages/ui` to the new DatePicker component in `packages/apollo-ui`. This migration involves component name changes, import path updates, and several API modifications.

- **Props API changes**: Several props removed, renamed, or modified
- **Enhanced functionality**: New props for label/helper text decorators
- **Underlying library**: Both use `react-datepicker` but with different configurations

### Props Changes

#### New Props (Apollo DatePicker)
- `labelDecorator` – augment label with additional content
- `helperTextDecorator` – augment helper text with additional content
- `required` – mark field as required with visual indicator
- `size` – control input size (`"small"` | `"medium"`)
- `fullWidth` – make input take full width of container

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-datepicker">DatePicker</StorybookLink> docs.

---

## Modal Component

The Modal component has undergone a **complete architectural redesign** in the new Apollo package. The migration involves significant changes from a monolithic component to a compositional pattern, requiring substantial code updates.

### Key Changes Overview

- **Compositional architecture**: Single `<Modal>` component → `Modal.Root`, `Modal.Header`, `Modal.Content`, `Modal.Footer`, `Modal.CloseButton`
- **Import changes**: Different import paths and component structure
- **Props API redesign**: Built-in button props removed, manual composition required
- **Event handling changes**: `onClose` → `onOpenChange` with different signature
- **Underlying library change**: MUI Base → Base UI components
- **CSS class names updated**: Legacy class names changed to new Apollo naming
- **Removed built-in functionality**: Automatic button generation, size variants, scrollable content prop

### Props Changes

#### Removed Props (Legacy → Apollo)
- `header` → Use `<Modal.Header>` component
- `footer` → Use `<Modal.Footer>` component
- `icon` → Pass to `<Modal.Header icon={...}>`
- `onClose: () => void` → Use `onOpenChange: (open: boolean) => void`
- `onOk`, `onCancel`, `onDelete` → Manual button composition
- `okButtonText`, `cancelButtonText`, `deleteButtonText` → Manual button text
- `disabledOkButton`, `disabledCancelButton`, `disabledDeleteButton` → Manual button disabled state
- `hideCloseIcon` → Conditionally render `<Modal.CloseButton>`
- `size` → Use CSS styling or className
- `minWidth`, `maxWidth` → Use CSS styling or className
- `scrollableContent` → Content automatically scrollable
- `closeAfterPressEsc`, `closeAfterClickBackdrop` → Use `dismissible` prop

#### New Props
- `dismissible`: Controls backdrop/ESC dismissal (replaces `closeAfterPressEsc` + `closeAfterClickBackdrop`)
- `defaultOpen`: Uncontrolled initial open state
- `onOpenChangeComplete`: Callback when animation completes
- `rootProps`, `portalProps`, `backdropProps`, `popupProps` for Advanced customization

### Component Structure Changes

#### Legacy (Monolithic)
<DiffCodeBlock
  code={`- import { Modal } from "@design-systems/apollo-ui"
-
- <Modal
-   open={isOpen}
-   onClose={() => setIsOpen(false)}
-   header="Confirm Action"
-   onOk={handleConfirm}
-   onCancel={() => setIsOpen(false)}
-   okButtonText="Confirm"
-   cancelButtonText="Cancel"
- >
-   Are you sure you want to proceed?
- </Modal>`}
/>

#### Apollo (Compositional)
<DiffCodeBlock
  code={`+ import { Modal, Button, Typography } from "@apollo/ui"
+
+ <Modal.Root
+   open={isOpen}
+   onOpenChange={(open) => setIsOpen(open)}
+ >
+   <Modal.Header>
+     <Typography level="titleMedium">Confirm Action</Typography>
+     <Modal.CloseButton />
+   </Modal.Header>
+   <Modal.Content>
+     Are you sure you want to proceed?
+   </Modal.Content>
+   <Modal.Footer>
+     <Button variant="outline" onClick={() => setIsOpen(false)}>
+       Cancel
+     </Button>
+     <Button onClick={handleConfirm}>
+       Confirm
+     </Button>
+   </Modal.Footer>
+ </Modal.Root>`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-feedback-modal">Modal</StorybookLink> docs.

---

## Radio Component

- **Label prop removed**: `label` prop no longer supported, use `children` for label content
- **Label placement removed**: Labels are now always positioned to the right of the radio button
- **Value prop type changed**: `string | number` → `string` only
- **RadioGroup onChange prop renamed**: `onChange` → `onValueChange` with signature `(value, event?) => void` → `(value, event) => void`
- **RadioGroup direction default changed**: `"vertical"` → `"vertical"` (unchanged)
- **Slot-based architecture removed**: MUI Base slots replaced with Base UI components
- **CSS class hooks changed**: Legacy class names updated to new Apollo naming
- **New labelProps support**: Added `labelProps` for customizing the label element

### Label Handling

The `label` prop has been removed in favor of using `children` for label content:

<DiffCodeBlock
  code={`- <Radio label="Option 1" value="option1" />
+ <Radio value="option1">Option 1</Radio>

- <Radio label="Option 2" value="option2" />
+ <Radio value="option2">Option 2</Radio>`}
/>

### Value Type

Radio values are now restricted to strings only:

<DiffCodeBlock
  code={`- <Radio value={1} label="First Option" />
- <Radio value={2} label="Second Option" />
+ <Radio value="1">First Option</Radio>
+ <Radio value="2">Second Option</Radio>`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-radio">Radio</StorybookLink> docs.

---

## Select Component

- Under the hood built on `@mui/base/Select` with `slots`/`slotProps` -> `@base-ui-components/react/select`
- Options
  - Legacy: use `<Option>` elements (from `@design-systems/apollo-ui`) – `label` optional (can render children)
  - Apollo: use `<Select.Option>` (or named `Option`) – `label` is the display text used to render the selected value
- Import component
<ul>
  <DiffCodeBlock
  code={`- import { Select, Option } from "@design-systems/apollo-ui"
+ import { Select } from "@apollo/ui"`}
  />
</ul
  >
- onChange MUI‑style `(event, value)` -> value‑first `(value, event?)`
<ul>
  <DiffCodeBlock
  code={`- <Select value={val} onChange={(e, v) => setVal(v)} />
+ <Select value={val} onChange={(v) => setVal(v)} />`}
  />
</ul>
- Removed `multiple` -> use Autocomplete component instead
- Removed legacy appearance props: `variant`, `color`
- Removed customization props: `slots`, `slotProps` ->  `className`, `fieldProps`
- Option element
<ul>
  <DiffCodeBlock
    code={`- <Option value={10}>Ten</Option>
+ <Select.Option label="Ten" value={10} />`}
  />
</ul>
- Removed customization props: `slots`, `slotProps` ->  `className`, `fieldProps`
<ul>
  <DiffCodeBlock
    code={`- <Select slots={{ root: MyButton, listbox: AnimatedListbox }} slotProps={{ listbox: { className: "my-list" } }} />
+ <Select className="my-select" fieldProps={{ className: "my-field" }} />`}
  />
</ul>

Notes
- If you relied on MUI Base `slots`/`slotProps`, migrate those customizations to composition (wrap with your own container) and className‑based styling.
- For per‑option custom visuals/actions, prefer pre‑rendered `label` content or handle behavior in `onChange`.


For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-select">Select</StorybookLink> docs.

---

## Switch Component

- **Label placement removed**: `labelPlacement` prop no longer supported (always left-aligned)
- **Field integration added**: New `fieldProps` for form field integration
- **onChange signature changed**: Standard HTML event → `(event: Event) => void`
- **onCheckedChange added**: New `(checked: boolean, event: Event) => void` callback
- **Action text support**: New `actionText` prop for additional context
- **Label decorators**: New `labelDecorator` and `required` props for enhanced labeling
- **Slot-based architecture removed**: MUI Base slots replaced with Base UI components
- **CSS class hooks changed**: Legacy class names updated to new Apollo naming

### Event Handling

The event handling has been enhanced with separate callbacks for different use cases:

<DiffCodeBlock
  code={`- <Switch
-   onChange={(e) => setEnabled(e.target.checked)}
- />
+ <Switch
+   onChange={(event) => console.log('Switch toggled')}
+   onCheckedChange={(checked, event) => setEnabled(checked)}
+ />`}
/>

### Action Text

New `actionText` prop provides additional context about the switch state:

<DiffCodeBlock
  code={`- <Switch
-   label="On/Off"
- />
+ // New functionality - action text
+ <Switch
+   label="Dark Mode" // This is label of form
+   actionText="On/Off"
+ />`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-switch">Switch</StorybookLink> docs.

---

## Textarea Component

The Textarea component is a **new dedicated component** that replaces the legacy Input component's `multiline` functionality. This change provides better separation of concerns and improved API design for multi-line text input.

- **Legacy Input `multiline` prop removed**: The `multiline` prop on Input component is no longer supported
- **Dedicated Textarea component**: Multi-line text input now requires the dedicated Textarea component
- **Import change**: Import `Textarea` instead of using `Input` with `multiline` prop
- **Props consolidation**: Textarea-specific props (`rows`, `minRows`, `maxRows`) are now properly typed and documented
- **Enhanced functionality**: New features like character counting and auto-resizing are built specifically for textarea use cases


<DiffCodeBlock
  code={`- <Input
-   multiline
-   label="Description"
-   placeholder="Enter description"
-   rows={4}
-   minRows={2}
-   maxRows={6}
- />
+ <Textarea
+   label="Description"
+   placeholder="Enter description"
+   rows={4}
+   minRows={2}
+   maxRows={6}
+ />`}
/>

For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-textarea">Textarea</StorybookLink> docs.

---

## Toast Component

The Toast component has undergone a **complete architectural redesign** in the new Apollo package. The migration involves significant changes to the API, usage patterns, and underlying implementation. This is one of the most substantial changes in the design system migration.

### Key Changes Overview

- **Complete API redesign**: Hook-based approach with `useToast()` instead of component-based rendering
- **Provider architecture**: New `ToastProvider` with different configuration options
- **Change hook usage**: Replace `showToast/showSuccessToast/showErrorToast` with `add`
- **Prop name changes**: `severity` → `type`, `autoHideDuration` → `timeout`
- **Type values changed**: `"info"` → `"information"`
- **New functionality**: Enhanced positioning, decorators, and width control
- **Removed functionality**: Direct component rendering, `lineClamp`, `transitionTime` props
- **Import changes**: Different import paths and component structure


For more information, refer to the <StorybookLink page="apollo∕ui-components-feedback-toast">Toast</StorybookLink> docs.

---

## Typography Component

The Typography component has been updated to align with Material Design 3 (M3) typography guidelines. This includes changes to the level names, color options, alignment, and other props.

- **Default level changed**: `"body-1"` → `"bodyLarge"`
- **Level naming convention changed**: Hyphenated names → camelCase names
   - `body-1` → `bodyLarge` (primary body text)
   - `body-2` → `bodyMedium` (secondary body text)
   - For smaller text, consider `bodySmall`
   - Legacy headings remain supported but consider M3 alternatives:
     - `h1` → `headlineLarge` or `displayLarge` (for hero text)
     - `h2` → `headlineMedium` or `displayMedium`
     - `h3` → `headlineSmall` or `titleLarge`
     - `h4` → `titleMedium`
     - `h5` → `titleSmall`
   - `caption` → remains `caption` or consider `labelSmall`
- **Color options expanded**: `"primary" | "danger"` → `"default" | "negative" | "warning" | "success" | "process" | "primary" | "secondary" | "tertiary"`
- **Color name changed**: `"danger"` → `"negative"`
- **Alignment options reduced**: `"inherit"` alignment option removed
- **Decorator props removed**: `startDecorator` and `endDecorator` props no longer supported
- **Ref handling changed**: `forwardRef` implementation removed, standard ref passing

For more information, refer to the <StorybookLink page="apollo∕ui-components-data-display-typography">Typography</StorybookLink> docs.

---

## UploadBox Component

The UploadBox component has been significantly enhanced in the new Apollo package with improved field integration, new upload hooks, and better error handling. While the core API remains similar, there are several important changes to be aware of.

- **Field integration**: Now extends `FieldProps` instead of `FormControlProps` for better form integration
- **New upload hooks**: Added `useUploadSingleFile` and `useUploadMultipleFile` for easier state management
- **Enhanced error handling**: New `errorOnClose` prop and improved error state management
- **Upload button customization**: New `uploadButtonText` prop for button text customization


For more information, refer to the <StorybookLink page="apollo∕ui-components-inputs-uploadbox">UploadBox</StorybookLink> docs.

---

## More components

We'll add more component sections as they land in `@apollo/ui`. If something you need is missing, please file a request in the Process page or contact the design systems team.

</div>

## Help 🙏

If you have any questions, feedback, or need help with the migration, please reach out to the design systems team. We're here to support you!
